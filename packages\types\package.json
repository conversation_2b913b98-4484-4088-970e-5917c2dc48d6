{"name": "@canmoms/types", "version": "1.0.0", "description": "Shared TypeScript type definitions for CanMoms platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "files": ["dist"], "keywords": ["typescript", "types", "canmoms"], "author": "CanMoms Team", "license": "MIT", "devDependencies": {"typescript": "^5.0.0", "rimraf": "^5.0.0"}, "publishConfig": {"access": "restricted"}}