{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "emitDeclarationOnly": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "baseUrl": ".", "paths": {"@canmoms/shared/*": ["packages/shared/src/*"], "@canmoms/types/*": ["packages/types/src/*"], "@canmoms/utils/*": ["packages/utils/src/*"]}}, "include": ["apps/*/src/**/*", "packages/*/src/**/*"], "exclude": ["node_modules", "dist", "build", "coverage", "**/*.spec.ts", "**/*.test.ts"], "references": [{"path": "./apps/backend"}, {"path": "./apps/web"}]}