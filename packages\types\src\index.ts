// User related types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  dateOfBirth?: Date;
  profilePicture?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile extends User {
  bio?: string;
  location?: string;
  children?: Child[];
  interests?: string[];
}

// Child related types
export interface Child {
  id: string;
  name: string;
  dateOfBirth: Date;
  gender?: 'male' | 'female' | 'other';
  parentId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Community related types
export interface Community {
  id: string;
  name: string;
  description: string;
  isPrivate: boolean;
  memberCount: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CommunityMember {
  id: string;
  userId: string;
  communityId: string;
  role: 'admin' | 'moderator' | 'member';
  joinedAt: Date;
}

// Post related types
export interface Post {
  id: string;
  title: string;
  content: string;
  authorId: string;
  communityId?: string;
  tags?: string[];
  likes: number;
  comments: number;
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Comment {
  id: string;
  content: string;
  authorId: string;
  postId: string;
  parentCommentId?: string;
  likes: number;
  createdAt: Date;
  updatedAt: Date;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// Event types
export interface Event {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  location?: string;
  isVirtual: boolean;
  organizerId: string;
  communityId?: string;
  maxAttendees?: number;
  currentAttendees: number;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface EventAttendee {
  id: string;
  userId: string;
  eventId: string;
  status: 'attending' | 'maybe' | 'not_attending';
  registeredAt: Date;
}
